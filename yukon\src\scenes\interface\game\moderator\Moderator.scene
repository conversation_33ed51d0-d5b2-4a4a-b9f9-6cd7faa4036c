{"id": "d44a536e-8824-4c11-8cdd-649f5629a29c", "sceneType": "PREFAB", "settings": {"compilerInsertSpaces": true, "javaScriptInitFieldsInConstructor": true, "exportClass": true, "autoImport": true, "superClassName": "BaseContainer", "preloadMethodName": "", "preloadPackFiles": [], "createMethodName": "", "borderWidth": 1520, "borderHeight": 960}, "displayList": [{"type": "Container", "id": "2c529465-d587-4faf-91dd-fb51fe023b47", "label": "container_1", "x": 760, "y": 480, "list": [{"type": "Rectangle", "id": "77fd1714-b7d5-4184-bd2c-3db23c8b9ad3", "label": "block", "components": ["Interactive"], "isFilled": true, "fillColor": "#000000", "fillAlpha": 0.2, "width": 1520, "height": 960}, {"type": "NinePatchContainer", "id": "f9229e1c-d5c7-4cf7-8d3f-f7460b47757c", "label": "bg", "texture": {"key": "prompt", "frame": "window"}, "y": -40, "width": 708, "height": 584, "marginLeft": 50, "marginRight": 50, "marginTop": 50, "marginBottom": 50}, {"type": "NinePatchContainer", "id": "1496d353-12ee-467f-b7b6-63f2c6e336f0", "label": "button", "scope": "CLASS", "components": ["<PERSON><PERSON>"], "Button.spriteName": "window-button", "Button.callback": "() => this.onClick()", "texture": {"key": "prompt", "frame": "window-button"}, "y": 127, "width": 558, "height": 105, "marginLeft": 50, "marginRight": 50, "marginTop": 50, "marginBottom": 50}, {"type": "Text", "id": "71bfc37a-0736-47e9-95b1-471b649b7138", "label": "buttonText", "scope": "CLASS", "y": 127, "originX": 0.5, "originY": 0.5, "text": "Become a Secret Agent", "fontFamily": "<PERSON><PERSON>", "fontSize": "40px", "fontStyle": "bold"}, {"type": "Text", "id": "5e32e41d-5889-463a-8973-e1c667b663ce", "label": "text", "x": -2, "y": -13, "originX": 0.5, "originY": 0.5, "text": "Please click a player to Ignore or\nReport the player to a moderator.", "fixedWidth": 400, "fixedHeight": 80, "align": "center", "fontFamily": "<PERSON><PERSON>", "fontSize": "32px", "color": "#000"}, {"type": "Text", "id": "96b0d404-05b7-4afa-8248-0f4e7aa1b18c", "label": "title", "y": -147, "originX": 0.5, "text": "MODERATOR ONLINE", "fixedWidth": 600, "align": "center", "fontFamily": "CCFaceFront", "fontSize": "40px", "fontStyle": "italic", "stroke": "#003366", "strokeThickness": 9, "shadow.stroke": true, "shadow.color": "#003366", "shadow.blur": 3}, {"type": "Image", "id": "89c11358-9fb7-4200-a165-b7290734c558", "label": "mod", "texture": {"key": "main", "frame": "mod_smaller"}, "x": -1, "y": -237, "originX": 0.504950495049505, "originY": 0.504950495049505}, {"type": "Image", "id": "66f04d86-8a45-4291-a87e-54a44592dab4", "label": "blueButton", "components": ["<PERSON><PERSON>"], "Button.spriteName": "blue-button", "Button.callback": "() => this.close()", "texture": {"key": "main", "frame": "blue-button"}, "x": 295, "y": -273}, {"type": "Image", "id": "5453ea33-e5bf-4be5-a2f0-497c75d3594f", "label": "blueX", "texture": {"key": "main", "frame": "blue-x"}, "x": 295, "y": -275}]}], "plainObjects": [], "meta": {"app": "Phaser Editor 2D - Scene Editor", "url": "https://phasereditor2d.com", "contentType": "phasereditor2d.core.scene.SceneContentType", "version": 5}}