{"name": "yukon", "version": "1.10.0-beta", "scripts": {"dev": "webpack serve", "build": "webpack --mode production --env obfuscate=false", "build-crumbs": "node ./utils/build-crumbs.js"}, "repository": {"type": "git", "url": "git+https://github.com/wizguin/yukon.git"}, "author": "wizguin", "license": "MIT", "bugs": {"url": "https://github.com/wizguin/yukon/issues"}, "homepage": "https://github.com/wizguin/yukon#readme", "devDependencies": {"@babel/core": "^7.24.3", "@babel/preset-env": "^7.24.3", "babel-loader": "^9.1.3", "compression-webpack-plugin": "^10.0.0", "core-js": "^3.36.1", "css-loader": "^7.1.2", "howler": "^2.2.4", "html-webpack-plugin": "^5.6.0", "javascript-obfuscator": "^4.1.0", "phasereditor2d-launcher": "^3.67.0", "phasereditor2d-ninepatch-plugin": "^1.2.0", "socket.io-client": "^4.7.5", "source-map": "^0.7.4", "style-loader": "^4.0.0", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2", "webpack-obfuscator": "^3.5.1"}}