{"id": "0d5e7ada-c8a0-426c-9a14-e3e6b36b143f", "sceneType": "SCENE", "settings": {"compilerInsertSpaces": true, "javaScriptInitFieldsInConstructor": true, "exportClass": true, "superClassName": "BaseScene", "preloadPackFiles": [], "createMethodName": "_create", "sceneKey": "<PERSON><PERSON>", "borderWidth": 1520, "borderHeight": 960}, "displayList": [{"type": "Image", "id": "b0ba7909-2225-4c6d-844f-fcab09ff0a6d", "label": "bg", "components": [], "texture": {"key": "load", "frame": "bg"}, "y": 1, "originX": 0, "originY": 0}, {"type": "Sprite", "id": "d16ac858-4ea0-46e8-93ab-a50f3a460376", "label": "backButton", "components": ["SimpleButton", "Animation"], "SimpleButton.callback": "() => this.onBackClick()", "Animation.key": "small-button", "Animation.end": 3, "Animation.repeat": 0, "Animation.onHover": true, "texture": {"key": "login", "frame": "small-button"}, "x": 760, "y": 876}, {"type": "Sprite", "id": "54a9d69a-d9ea-4d03-a10b-025ef91c9263", "label": "createButton", "components": ["SimpleButton", "Animation"], "SimpleButton.callback": "() => this.onCreateClick()", "Animation.key": "large-button", "Animation.end": 3, "Animation.repeat": 0, "Animation.onHover": true, "texture": {"key": "login", "frame": "large-button"}, "x": 760, "y": 728}, {"type": "Sprite", "id": "e7d1dae8-bad8-408a-864c-184cec80ed19", "label": "forgotButton", "components": ["SimpleButton", "Animation"], "Animation.key": "small-button", "Animation.end": 3, "Animation.repeat": 0, "Animation.onHover": true, "texture": {"key": "login", "frame": "small-button"}, "x": 760, "y": 604}, {"type": "Image", "id": "b92b7009-e006-476e-a579-f883f6ec6feb", "label": "note", "components": [], "texture": {"key": "login", "frame": "note"}, "x": 1182, "y": 556}, {"type": "Text", "id": "5d9ec495-590b-4df6-80bb-0fffebb15251", "label": "backText", "components": [], "x": 760, "y": 876, "originX": 0.5, "originY": 0.5, "text": "Back", "lineSpacing": 25, "align": "right", "fontFamily": "<PERSON><PERSON>", "fontSize": "30px", "color": "#ffffffff"}, {"type": "Text", "id": "6e080082-1f94-49de-93ac-a33f0a579e66", "label": "registerText2", "components": [], "x": 760, "y": 747, "originX": 0.5, "originY": 0.5, "text": "Create a free account now", "lineSpacing": 25, "align": "right", "fontFamily": "<PERSON><PERSON>", "fontSize": "35px", "color": "#ffffffff"}, {"type": "Text", "id": "d7f0475d-0213-4ab6-bab2-d8be86122e35", "label": "registerText", "components": [], "x": 760, "y": 713, "originX": 0.5, "originY": 0.5, "text": "Don't have a penguin?", "lineSpacing": 25, "align": "right", "fontFamily": "<PERSON><PERSON>", "fontSize": "30px", "color": "#000000ff"}, {"type": "Text", "id": "731adc13-a878-47cf-866b-555084400e4c", "label": "forgotText", "components": [], "x": 760, "y": 604, "originX": 0.5, "originY": 0.5, "text": "Forgot your password?", "lineSpacing": 25, "align": "right", "fontFamily": "<PERSON><PERSON>", "fontSize": "30px", "color": "#ffffffff"}, {"type": "Sprite", "id": "d54f4f4f-2569-4441-a370-6e29e4af5074", "label": "loginButton", "components": ["<PERSON><PERSON>"], "Button.spriteName": "login-button", "Button.callback": "() => this.onLoginSubmit()", "texture": {"key": "login", "frame": "login-button"}, "x": 760, "y": 483}, {"type": "Text", "id": "ad2e73b7-d14d-456d-8cb0-7ea3cf7a150b", "label": "loginText", "components": [], "x": 760, "y": 483, "originX": 0.5, "originY": 0.5, "text": "<PERSON><PERSON>", "lineSpacing": 25, "align": "right", "fontFamily": "<PERSON><PERSON>", "fontSize": "38px", "color": "#ffffffff"}, {"type": "Text", "id": "6ff586b9-fbe0-49f6-9801-71f273c1df58", "label": "passwordText", "components": [], "x": 503, "y": 258, "originY": 0.5, "text": "Password:", "lineSpacing": 25, "align": "right", "fontFamily": "<PERSON><PERSON>", "fontSize": "30px", "color": "#000000ff"}, {"type": "Text", "id": "dfe772c9-e24b-4230-a416-f95f1b14b3b3", "label": "usernameText", "components": [], "x": 448, "y": 200, "originY": 0.5, "text": "Penguin Name:", "lineSpacing": 25, "align": "right", "fontFamily": "<PERSON><PERSON>", "fontSize": "30px", "color": "#000000ff"}, {"type": "Image", "id": "ffeb45b6-13e0-49b0-ad82-294a535c3ac4", "label": "password", "components": [], "texture": {"key": "login", "frame": "input"}, "x": 815, "y": 258}, {"type": "Image", "id": "f3dd63e7-4704-47f4-913d-b0daebe09d24", "label": "username", "components": [], "texture": {"key": "login", "frame": "input"}, "x": 815, "y": 200}, {"prefabId": "1a09293b-d8f0-454f-ad6c-dc0364729549", "id": "759943e1-d192-45c9-8562-671e6d1d788a", "unlock": ["x", "y"], "label": "checks", "scope": "CLASS", "components": [], "x": 568, "y": 328, "nestedPrefabs": []}, {"prefabId": "b1f49bb5-b7f5-4eb9-81d7-21aa125e487f", "id": "71f5ebab-c88b-4516-881b-0ac2f1827416", "unlock": ["visible", "x", "y"], "label": "waitPrompt", "scope": "CLASS", "components": [], "x": 760, "y": 480, "visible": false, "nestedPrefabs": []}, {"prefabId": "4a66782f-1b3a-41da-b0c3-3d9d3c24217c", "id": "864618dc-5334-40ca-af06-f3fb878ca5b1", "unlock": ["visible", "x", "y"], "label": "savePrompt", "scope": "CLASS", "components": [], "x": 760, "y": 480, "visible": false, "nestedPrefabs": []}], "plainObjects": [], "meta": {"app": "Phaser Editor 2D - Scene Editor", "url": "https://phasereditor2d.com", "contentType": "phasereditor2d.core.scene.SceneContentType", "version": 3}}