{"name": "yukon-server", "version": "1.10.1-beta", "description": "A server for https://github.com/wizguin/yukon", "scripts": {"dev": "babel-watch ./src/World.js Login Blizzard", "build": "rimraf dist && babel src -d dist --copy-files", "start": "pm2 start ecosystem.config.js", "stop": "pm2 stop ecosystem.config.js", "restart": "pm2 restart ecosystem.config.js", "list": "pm2 list", "logs": "pm2 logs", "monit": "pm2 monit", "secret-gen": "node ./utils/secret-gen.js"}, "repository": {"type": "git", "url": "git+https://github.com/wizguin/yukon-server.git"}, "author": "wizguin", "license": "MIT", "bugs": {"url": "https://github.com/wizguin/yukon-server/issues"}, "homepage": "https://github.com/wizguin/yukon-server#readme", "devDependencies": {"@babel/cli": "^7.24.1", "@babel/core": "^7.24.3", "@babel/node": "^7.23.9", "@babel/preset-env": "^7.24.3", "babel-plugin-module-resolver": "^5.0.0", "babel-watch": "^7.8.1", "eslint": "^8.57.0", "rimraf": "^5.0.5"}, "dependencies": {"bcrypt": "^5.1.1", "fastest-validator": "^1.17.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.9.2", "pm2": "^5.3.1", "rate-limiter-flexible": "^3.0.6", "sequelize": "^5.22.5", "socket.io": "^4.7.5", "uuid": "^9.0.1"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^6.0.3"}}