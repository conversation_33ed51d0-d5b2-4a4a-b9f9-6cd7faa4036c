{"id": "9f793ade-93a8-4ab2-bd68-ccd899fb9179", "sceneType": "PREFAB", "settings": {"compilerInsertSpaces": true, "snapWidth": 52, "snapHeight": 52, "javaScriptInitFieldsInConstructor": true, "exportClass": true, "superClassName": "BaseContainer", "preloadMethodName": "", "preloadPackFiles": [], "createMethodName": "", "borderWidth": 1520, "borderHeight": 960}, "displayList": [{"type": "Container", "id": "e1cfcd6e-44aa-4018-8bcb-60881317fada", "label": "container", "components": [], "x": 760, "y": 480, "list": [{"type": "Rectangle", "id": "123e74a3-9033-49c5-bed7-66d7338697a9", "label": "block", "components": ["Interactive"], "isFilled": true, "fillColor": "#000000", "fillAlpha": 0.2, "width": 1520, "height": 960}, {"type": "Image", "id": "8cee385b-9867-477b-a47c-4807e40b7441", "label": "igloo_bg", "components": [], "texture": {"key": "map", "frame": "igloo/bg"}, "x": -11, "y": -44}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "efbfa003-a5bf-4f09-8158-0c935ba8be69", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_27", "components": [], "x": -336, "y": -50, "scaleX": 0.9, "scaleY": 0.9, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "9bfaa046-9f97-49e2-85cb-4392427d506d", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_22", "components": [], "x": -277, "y": -30, "scaleX": 0.9, "scaleY": 0.9, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "ff65f6a2-de4d-4595-9769-4d9cd9f3afe0", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_21", "components": [], "x": -216, "y": -10, "scaleX": 0.9, "scaleY": 0.9, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "70df011b-bf01-4d02-acd8-c37cb78ba6bb", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_9", "components": [], "x": -156, "y": 9, "scaleX": 0.9, "scaleY": 0.9, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "d6734262-de5a-4316-a2dc-07343814130e", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_5", "components": [], "x": -107, "y": 61, "scaleX": 1, "scaleY": 1, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "f285a9df-6298-4631-8344-1c53d23874b5", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_16", "components": [], "x": -224, "y": 88, "scaleX": 1, "scaleY": 1, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "bab95ba9-1a97-4c58-85f5-51de855985d6", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_20", "components": [], "x": -150, "y": 119, "scaleX": 1, "scaleY": 1, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "b3e594dc-65af-4dfb-94a9-54718d4cefa6", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_19", "components": [], "x": -89, "y": 140, "scaleX": 1, "scaleY": 1, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "5f23734a-0049-4422-8d03-614244cae2e5", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_29", "components": [], "x": -439, "y": -16, "scaleX": 0.9, "scaleY": 0.9, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "6062a31e-f2ce-4790-bf14-1914130e1c15", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_28", "components": [], "x": -382, "y": 36, "scaleX": 1, "scaleY": 1, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "f91da99b-265d-4901-a7ae-61845ff2ee5e", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_18", "components": [], "x": -323, "y": 57, "scaleX": 1, "scaleY": 1, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "27136bac-dfaa-4f24-8518-ab3565c28df1", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_17", "components": [], "x": -286, "y": 116, "scaleX": 1, "scaleY": 1, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "ce29a8af-9a7e-44fb-ae62-23bfa91a3e82", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_10", "components": [], "x": 108, "y": 67, "scaleX": 1, "scaleY": 1, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "02de7593-ec15-4a2e-8f95-ee708a04cb6d", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_6", "components": [], "x": 81, "y": 117, "scaleX": 1, "scaleY": 1, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "aecffcb5-9efa-4a1d-8338-1912038d428b", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_8", "components": [], "x": 188, "y": 101, "scaleX": 1, "scaleY": 1, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "169bfc90-763e-4b33-a813-b297dff232a5", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_13", "components": [], "x": -121, "y": -144, "scaleX": 0.8, "scaleY": 0.8, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "f2fa682e-7fe2-482d-aa98-395202aed48a", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_12", "components": [], "x": -76, "y": -108, "scaleX": 0.8, "scaleY": 0.8, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "897ef7ad-a704-45cc-8ddc-7822df517e75", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_25", "components": [], "x": -35, "y": -67, "scaleX": 0.8, "scaleY": 0.8, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "1397ffd4-c1da-474c-a38e-38b8a1f3f6a8", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_2", "components": [], "x": -46, "y": -7, "scaleX": 0.9, "scaleY": 0.9, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "a2acf8c4-4cc9-40c6-9976-79931e7d1c99", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_14", "components": [], "x": 135, "y": -49, "scaleX": 0.9, "scaleY": 0.9, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "b705b4ca-58d6-4e2f-8b42-36cb291fc238", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_3", "components": [], "x": 95, "y": -28, "scaleX": 0.9, "scaleY": 0.9, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "01da9100-d4ce-4448-acfd-cb9738b25740", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_1", "components": [], "x": 44, "y": 6, "scaleX": 0.9, "scaleY": 0.9, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "55a89911-6924-49e5-999f-d188005974e6", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_0", "components": [], "x": -5, "y": 40, "scaleX": 0.9, "scaleY": 0.9, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "90cebc62-f3e9-4f9f-b5c6-54aecebe3212", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_15", "components": [], "x": 191, "y": -14, "scaleX": 0.9, "scaleY": 0.9, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "7fde4342-fd3d-481b-9ab0-22787c71dacd", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_23", "components": [], "x": -136, "y": -73, "scaleX": 0.8, "scaleY": 0.8, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "efe0d647-91b8-4569-9849-0fdefd57ce1d", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_24", "components": [], "x": -189, "y": -75, "scaleX": 0.8, "scaleY": 0.8, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "3444a2ea-f8f7-490e-ae66-7ae2304f1f0b", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_26", "components": [], "x": -31, "y": -180, "scaleX": 0.8, "scaleY": 0.8, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "e8d87e63-452f-4469-b793-8234cbabd398", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_7", "components": [], "x": 10, "y": -156, "scaleX": 0.8, "scaleY": 0.8, "flipX": false, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "73d5c237-fd17-4b4a-abe2-bf2cd27fd1a0", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_11", "components": [], "x": 71, "y": -140, "scaleX": 0.8, "scaleY": 0.8, "flipX": true, "flipY": false}, {"prefabId": "db5f353b-acdc-4124-a0f5-f96981200c80", "id": "2f23ba17-9411-4c7b-a1e5-73e9eec3dae3", "unlock": ["x", "y", "scaleX", "scaleY", "flipX", "flipY"], "label": "igloo_igloo_4", "components": [], "x": 37, "y": -107, "scaleX": 0.8, "scaleY": 0.8, "flipX": false, "flipY": false}, {"type": "Image", "id": "d53efe40-376e-4cc6-a15b-dfb0ed159bc7", "label": "igloo_note", "components": [], "texture": {"key": "map", "frame": "igloo/note"}, "x": -485, "y": -268}, {"type": "Image", "id": "af62013d-607b-4256-a491-261942b80644", "label": "grey_button", "components": ["<PERSON><PERSON>"], "Button.spriteName": "grey-button", "Button.callback": "() => this.visible = false", "texture": {"key": "main", "frame": "grey-button"}, "x": 476, "y": -286, "angle": 6}, {"type": "Image", "id": "7b6b5267-1b9a-4fb1-95b0-6b3f0f0fb333", "label": "grey_x", "components": [], "texture": {"key": "main", "frame": "grey-x"}, "x": 476, "y": -288, "angle": 6}, {"type": "Image", "id": "d0e542f4-15de-4092-bd3a-a31a1e0bbec6", "label": "spinner", "scope": "CLASS", "components": [], "texture": {"key": "map", "frame": "igloo/spinner"}, "x": -19, "y": -55, "originX": 0.5076923076923077}, {"type": "Container", "id": "45cbd5aa-ebf1-43ce-9f40-73d7d11a58ff", "label": "panel", "scope": "CLASS", "components": [], "x": 268, "y": -238, "list": [{"type": "Image", "id": "0d9804c6-6a48-4ba9-af80-89679f23a786", "label": "igloo_card", "components": [], "texture": {"key": "map", "frame": "igloo/card"}, "x": 197, "y": 318}, {"type": "Image", "id": "7192d193-b7ad-47b0-86b4-a2796f557856", "label": "igloo_tape", "components": [], "texture": {"key": "map", "frame": "igloo/tape"}, "x": 5, "originX": 0.5017421602787456}, {"type": "Image", "id": "4e36026d-3b7d-4007-a57d-e84e78537bd7", "label": "igloo_item_large", "components": ["<PERSON><PERSON>"], "Button.spriteName": "igloo/item_large", "Button.callback": "() => this.onIglooClick()", "Button.activeFrame": false, "texture": {"key": "map", "frame": "igloo/item_large"}, "x": 183, "y": 72}, {"type": "Image", "id": "86fc79de-83a1-4397-b1b0-bd45a9bd9e1a", "label": "igloo_icon_player", "components": [], "texture": {"key": "map", "frame": "igloo/icon_player"}, "x": 18, "y": 72, "originX": 0.****************, "originY": 0.****************}, {"type": "Text", "id": "8f18b6fc-d6a7-40da-8487-0c5d8675270d", "label": "username", "components": [], "x": 180, "y": 72, "originX": 0.5, "originY": 0.5, "fixedWidth": 270, "fontFamily": "<PERSON><PERSON>", "fontSize": "24px", "color": "#000"}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "b4ad7b54-24cd-4e69-b4e0-b8ed5e2e2e43", "unlock": ["x", "y"], "label": "iglooItem_7", "components": [], "x": 155, "y": 549, "nestedPrefabs": []}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "7389ebc2-225c-4f21-9e4a-58c1d3c0afde", "unlock": ["x", "y"], "label": "iglooItem_6", "components": [], "x": 155, "y": 497, "nestedPrefabs": []}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "593ab029-6de6-4edc-ba30-e7ad3fdc04cc", "unlock": ["x", "y"], "label": "iglooItem_5", "components": [], "x": 155, "y": 445, "nestedPrefabs": []}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "5dbd37c9-55d0-43c1-940d-4087cf5944ab", "unlock": ["x", "y"], "label": "iglooItem_4", "components": [], "x": 155, "y": 393, "nestedPrefabs": []}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "d85c52aa-7b0b-49e0-8c9b-899fe1830966", "unlock": ["x", "y"], "label": "iglooItem_3", "components": [], "x": 155, "y": 341, "nestedPrefabs": []}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "b75f4b5c-afd1-42b7-9e70-73ca9fe90bfb", "unlock": ["x", "y"], "label": "iglooItem_2", "components": [], "x": 155, "y": 289, "nestedPrefabs": []}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "44e57939-2e00-429f-97cf-b163a0147f69", "unlock": ["x", "y"], "label": "iglooItem_1", "components": [], "x": 155, "y": 237, "nestedPrefabs": []}, {"prefabId": "1cc2585a-e5f3-48aa-ba82-cf1dc70a902c", "id": "7a199c85-4d85-466f-9ced-f1e6b418af81", "unlock": ["x", "y"], "label": "iglooItem", "components": [], "x": 155, "y": 185, "nestedPrefabs": []}, {"type": "Text", "id": "c2811f19-01cb-4c16-881e-5d242be4e96a", "label": "open", "components": [], "y": 126, "text": "Open Igloos", "fontFamily": "Burbank Small", "fontSize": "22px", "fontStyle": "bold", "color": "#999"}, {"type": "Container", "id": "e1f5687b-5fa9-4500-80e3-7ee473c70128", "label": "down", "scope": "CLASS", "components": [], "x": 358, "y": 549, "list": [{"type": "Image", "id": "61e5c1eb-6402-45c1-8607-b34e41d31a34", "label": "downButton", "scope": "CLASS", "components": ["<PERSON><PERSON>"], "Button.spriteName": "grey-button", "Button.callback": "() => this.nextPage()", "texture": {"key": "main", "frame": "grey-button"}, "y": 2}, {"type": "Image", "id": "e8a64295-995f-4067-875d-286ce73f0f51", "label": "downArrow", "components": [], "texture": {"key": "main", "frame": "grey-arrow"}, "flipY": true}]}, {"type": "Container", "id": "7d998331-bb1f-4151-ade2-cc27e0e56973", "label": "up", "scope": "CLASS", "components": [], "x": 358, "y": 180, "list": [{"type": "Image", "id": "0a19c1f5-ddb6-49ec-adba-d25330d246fc", "label": "upButton", "scope": "CLASS", "components": ["<PERSON><PERSON>"], "Button.spriteName": "grey-button", "Button.callback": "() => this.prevPage()", "texture": {"key": "main", "frame": "grey-button"}, "y": 2}, {"type": "Image", "id": "fdb66dac-1879-4780-be1c-d0f874dc7ab1", "label": "upArrow", "components": [], "texture": {"key": "main", "frame": "grey-arrow"}}]}]}]}], "plainObjects": [], "meta": {"app": "Phaser Editor 2D - Scene Editor", "url": "https://phasereditor2d.com", "contentType": "phasereditor2d.core.scene.SceneContentType", "version": 3}, "lists": [{"id": "59660e8c-afd9-429a-bc9b-19550b385485", "label": "iglooSprites", "objectIds": ["55a89911-6924-49e5-999f-d188005974e6", "01da9100-d4ce-4448-acfd-cb9738b25740", "1397ffd4-c1da-474c-a38e-38b8a1f3f6a8", "b705b4ca-58d6-4e2f-8b42-36cb291fc238", "2f23ba17-9411-4c7b-a1e5-73e9eec3dae3", "d6734262-de5a-4316-a2dc-07343814130e", "02de7593-ec15-4a2e-8f95-ee708a04cb6d", "e8d87e63-452f-4469-b793-8234cbabd398", "aecffcb5-9efa-4a1d-8338-1912038d428b", "70df011b-bf01-4d02-acd8-c37cb78ba6bb", "ce29a8af-9a7e-44fb-ae62-23bfa91a3e82", "73d5c237-fd17-4b4a-abe2-bf2cd27fd1a0", "f2fa682e-7fe2-482d-aa98-395202aed48a", "169bfc90-763e-4b33-a813-b297dff232a5", "a2acf8c4-4cc9-40c6-9976-79931e7d1c99", "90cebc62-f3e9-4f9f-b5c6-54aecebe3212", "f285a9df-6298-4631-8344-1c53d23874b5", "27136bac-dfaa-4f24-8518-ab3565c28df1", "f91da99b-265d-4901-a7ae-61845ff2ee5e", "b3e594dc-65af-4dfb-94a9-54718d4cefa6", "bab95ba9-1a97-4c58-85f5-51de855985d6", "ff65f6a2-de4d-4595-9769-4d9cd9f3afe0", "9bfaa046-9f97-49e2-85cb-4392427d506d", "7fde4342-fd3d-481b-9ab0-22787c71dacd", "efe0d647-91b8-4569-9849-0fdefd57ce1d", "897ef7ad-a704-45cc-8ddc-7822df517e75", "3444a2ea-f8f7-490e-ae66-7ae2304f1f0b", "efbfa003-a5bf-4f09-8158-0c935ba8be69", "6062a31e-f2ce-4790-bf14-1914130e1c15", "5f23734a-0049-4422-8d03-614244cae2e5"]}, {"id": "0169b306-efe3-4b5c-bd8b-05bc5d08d32b", "label": "items", "objectIds": ["7a199c85-4d85-466f-9ced-f1e6b418af81", "44e57939-2e00-429f-97cf-b163a0147f69", "b75f4b5c-afd1-42b7-9e70-73ca9fe90bfb", "d85c52aa-7b0b-49e0-8c9b-899fe1830966", "5dbd37c9-55d0-43c1-940d-4087cf5944ab", "593ab029-6de6-4edc-ba30-e7ad3fdc04cc", "7389ebc2-225c-4f21-9e4a-58c1d3c0afde", "b4ad7b54-24cd-4e69-b4e0-b8ed5e2e2e43"]}]}