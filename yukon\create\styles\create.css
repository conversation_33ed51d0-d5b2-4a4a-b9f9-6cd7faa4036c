@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500&display=swap');

h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    color: var(--text)
}

.button {
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 500;
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    color: var(--text);
    background-color: var(--button);
    padding: 5px 15px;
    border: none;
    border-radius: 5px;
    transition: background ease 200ms;
    cursor: pointer;
}

.button:hover {
    background-color: var(--button-hover);
}

/* Modal */

.modal-container {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    z-index: 1101;
    background: none;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal {
    width: 60%;
    max-width: 500px;
    background-color: var(--bg);
    border-radius: 5px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    color: var(--text);
    position: relative;
    padding: 30px 50px 30px 50px;
}

.modal-close {
    position: absolute;
    font-family: 'Times New Roman', 'Times', serif;
    font-size: 30px;
    color: var(--text);
    top: 0;
    right: 0;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
    transition: color ease 200ms;
}

.modal-close:hover {
    color: var(--accent);
}

.modal h1 {
    margin: 15px 0 15px 0;
}

.modal p {
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 400;
    margin: 0 0 15px 0;
}

.fade {
    animation-name: fade;
    animation-duration: 0.2s;
}

@keyframes fade {
    from {opacity: 0;}
    to {opacity: 100;}
}

@media only screen and (max-width: 600px) {
    .modal {
        width: 100%;
        height: 100%;
        max-width: 600px;
        border-radius: 0;
        padding: 10px;
    }
}

/* Create form */

.modal form {
    width: 100%;
    margin: 0;
}

.form-group,
.modal .button {
    margin-bottom: 15px;
}

.form-group input,
.modal .button {
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    width: 100%;
    padding: 15px;
    line-height: 1.5;
    border: 1px solid transparent;
    border-radius: 5px;
}

.form-group input {
    font-weight: 400;
    color: var(--input-text);
    background-color: var(--input);
}

.form-group input:focus {
    color: var(--input-text);
    background-color: var(--input);
    border-color: #eceff4;
    box-shadow: 0 0 0 0.25rem rgb(255 255 255 / 25%);
    outline: 0;
}

.form-group .feedback {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: var(--danger);
    margin-top: 10px;
}

.form-group .invalid {
    border-color: var(--danger);
}

.form-group .invalid:focus {
    box-shadow: 0 0 0 0.25rem rgb(220 53 69 / 25%);
    border-color: var(--danger);
}
