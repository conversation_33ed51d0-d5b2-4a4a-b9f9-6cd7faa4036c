{"id": "8808181a-7877-4aef-982e-f4ba2441da75", "sceneType": "PREFAB", "settings": {"compilerInsertSpaces": true, "javaScriptInitFieldsInConstructor": true, "exportClass": true, "superClassName": "BaseContainer", "preloadMethodName": "", "preloadPackFiles": [], "createMethodName": "", "borderWidth": 1520, "borderHeight": 960}, "displayList": [{"type": "Container", "id": "6ac0cd17-7552-4894-a5cc-93d2081afaa3", "label": "container_1", "x": 760, "y": 480, "list": [{"type": "Image", "id": "cc52a380-fae2-4ca6-bb0c-90a7a5ae503b", "label": "report_button", "scope": "CLASS", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "blue-button", "Button.callback": "() => this.onReportClick()", "ShowHint.text": "report_player_hint", "texture": {"key": "main", "frame": "blue-button-disabled"}, "x": 150}, {"type": "Image", "id": "e7420ff2-8996-4651-9900-bd00f84538fe", "label": "ignore_button", "scope": "CLASS", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "blue-button", "Button.callback": "() => this.onIgnoreClick()", "ShowHint.text": "add_ignore_hint", "texture": {"key": "main", "frame": "blue-button-disabled"}, "x": 90}, {"type": "Image", "id": "8df3aa1e-b498-4020-9be9-90c47a525198", "label": "mail_button", "scope": "CLASS", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "blue-button", "Button.callback": "() => this.onMailClick()", "ShowHint.text": "send_mail_hint", "texture": {"key": "main", "frame": "blue-button-disabled"}, "x": 30}, {"type": "Image", "id": "6a228319-6c4c-4f67-89ce-a53d0c26004e", "label": "igloo_button", "scope": "CLASS", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "blue-button", "Button.callback": "() => this.onIglooClick()", "ShowHint.text": "visit_home_hint", "texture": {"key": "main", "frame": "blue-button-disabled"}, "x": -30}, {"type": "Image", "id": "ebce1c1c-faeb-45a9-ba2c-1559e185f215", "label": "profile_button", "scope": "CLASS", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "blue-button", "Button.callback": "() => this.onFindClick()", "ShowHint.text": "profile_hint", "texture": {"key": "main", "frame": "blue-button-disabled"}, "x": -90}, {"type": "Image", "id": "7490ec55-9795-4b55-bcf2-7170ee493ffd", "label": "buddy_button", "scope": "CLASS", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "blue-button", "Button.callback": "() => this.onBuddyClick()", "ShowHint.text": "add_buddy_hint", "texture": {"key": "main", "frame": "blue-button-disabled"}, "x": -150}, {"type": "Image", "id": "682a26a5-7f79-4e67-9be0-4992ab794f04", "label": "report_icon", "scope": "CLASS", "texture": {"key": "main", "frame": "mod-icon-disabled"}, "x": 150, "y": -1}, {"type": "Image", "id": "26ec7369-28a0-470f-b281-7dd6a3e7b31f", "label": "ignore_icon", "scope": "CLASS", "texture": {"key": "main", "frame": "ignore-icon-disabled"}, "x": 90, "y": -2}, {"type": "Image", "id": "2b0aee28-83bc-4f3e-bc89-4c338f5558d0", "label": "mail_icon", "scope": "CLASS", "texture": {"key": "main", "frame": "mail-icon-disabled"}, "x": 30, "y": -2}, {"type": "Image", "id": "f0daa280-4416-48d2-986e-41f34099de22", "label": "igloo_icon", "scope": "CLASS", "texture": {"key": "main", "frame": "igloo-icon-disabled"}, "x": -30, "y": -2}, {"type": "Image", "id": "49810571-d58b-4ed6-883f-1f1baffe6403", "label": "profile_icon", "scope": "CLASS", "texture": {"key": "main", "frame": "help-icon-disabled"}, "x": -90, "y": -2}, {"type": "Image", "id": "92ce6fc2-cb90-444d-9a33-0566100ebc0f", "label": "buddy_icon", "scope": "CLASS", "texture": {"key": "main", "frame": "buddies-icon-disabled"}, "x": -150, "y": -2}]}], "plainObjects": [], "meta": {"app": "Phaser Editor 2D - Scene Editor", "url": "https://phasereditor2d.com", "contentType": "phasereditor2d.core.scene.SceneContentType", "version": 5}}