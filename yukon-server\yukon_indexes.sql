-- Add primary keys and indexes
ALTER TABLE `auth_tokens` ADD PRIMARY KEY (`userId`,`selector`) USING BTREE;
ALTER TABLE `bans` ADD PRIMARY KEY (`id`), ADD KEY `userId` (`userId`), ADD KEY `moderatorId` (`moderatorId`);
ALTER TABLE `buddies` ADD PRIMARY KEY (`userId`,`buddyId`) USING BTREE, ADD KEY `buddies_ibfk_2` (`buddyId`);
ALTER TABLE `cards` ADD PRIMARY KEY (`userId`,`cardId`);
ALTER TABLE `furnitures` ADD PRIMARY KEY (`id`), ADD KEY `userId` (`userId`) USING BTREE;
ALTER TABLE `furniture_inventories` ADD PRIMARY KEY (`userId`,`itemId`) USING BTREE;
ALTER TABLE `igloos` ADD PRIMARY KEY (`userId`);
ALTER TABLE `igloo_inventories` ADD PRIMARY KEY (`userId`,`iglooId`) USING BTREE;
ALTER TABLE `ignores` ADD PRIMARY KEY (`userId`,`ignoreId`) USING BTREE, ADD KEY `ignores_ibfk_2` (`ignoreId`);
ALTER TABLE `inventories` ADD PRIMARY KEY (`userId`,`itemId`) USING BTREE;
ALTER TABLE `pets` ADD PRIMARY KEY (`id`), ADD KEY `userId` (`userId`), ADD KEY `feedPostcardId` (`feedPostcardId`) USING BTREE;
ALTER TABLE `postcards` ADD PRIMARY KEY (`id`), ADD KEY `userId` (`userId`), ADD KEY `senderId` (`senderId`);
ALTER TABLE `users` ADD PRIMARY KEY (`id`), ADD UNIQUE KEY `username` (`username`) USING BTREE;
ALTER TABLE `worlds` ADD PRIMARY KEY (`id`);

-- Add auto increment
ALTER TABLE `bans` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `furnitures` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `pets` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `postcards` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `users` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

-- Add foreign key constraints
ALTER TABLE `auth_tokens` ADD CONSTRAINT `auth_tokens_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `bans` ADD CONSTRAINT `bans_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `bans` ADD CONSTRAINT `bans_ibfk_2` FOREIGN KEY (`moderatorId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE SET NULL;
ALTER TABLE `buddies` ADD CONSTRAINT `buddies_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `buddies` ADD CONSTRAINT `buddies_ibfk_2` FOREIGN KEY (`buddyId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `cards` ADD CONSTRAINT `cards_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `furnitures` ADD CONSTRAINT `furnitures_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `furniture_inventories` ADD CONSTRAINT `furniture_inventories_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `igloos` ADD CONSTRAINT `igloos_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `igloo_inventories` ADD CONSTRAINT `igloo_inventories_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ignores` ADD CONSTRAINT `ignores_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ignores` ADD CONSTRAINT `ignores_ibfk_2` FOREIGN KEY (`ignoreId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `inventories` ADD CONSTRAINT `inventories_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `pets` ADD CONSTRAINT `pets_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `pets` ADD CONSTRAINT `pets_ibfk_2` FOREIGN KEY (`feedPostcardId`) REFERENCES `postcards` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `postcards` ADD CONSTRAINT `postcards_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `postcards` ADD CONSTRAINT `postcards_ibfk_2` FOREIGN KEY (`senderId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
