{"id": "20915166-b810-4ea6-b9c1-62e694ee975c", "sceneType": "PREFAB", "settings": {"compilerInsertSpaces": true, "javaScriptInitFieldsInConstructor": true, "exportClass": true, "autoImport": true, "superClassName": "BaseContainer", "preloadMethodName": "", "preloadPackFiles": [], "createMethodName": "", "borderWidth": 1520, "borderHeight": 960}, "displayList": [{"type": "Container", "id": "92e98aa4-f6b1-4b7b-bee2-08bc6a7b7bef", "label": "container_1", "list": [{"type": "Image", "id": "dd831ee5-3fa1-4d8e-adf4-273bf9ac6372", "label": "mat", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "mat/mat_2", "Button.activeFrame": false, "Button.pixelPerfect": true, "ShowHint.text": "card_hint", "texture": {"key": "dojo", "frame": "mat/mat_2"}, "originX": 0.5018450184501845}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "8f21b06e-4da5-476f-b271-43aa77e5fe8a", "unlock": ["x", "y", "visible"], "label": "done1", "scope": "CLASS", "x": 51, "y": -70, "visible": false}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "8792809f-6079-417f-bf2c-dc42c7f25042", "unlock": ["x", "y", "visible"], "label": "done0", "scope": "CLASS", "x": -49, "y": -70, "visible": false}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "b3111e02-4116-43ee-98a5-e560f362f912", "unlock": ["x", "y", "sit<PERSON>ram<PERSON>", "donePoint", "visible"], "label": "seat1", "scope": "CLASS", "sitFrame": 3, "donePoint": "done1", "x": 71, "y": -15, "visible": false}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "996b84da-2630-4710-9b87-35e06176a0d1", "unlock": ["x", "y", "sit<PERSON>ram<PERSON>", "donePoint", "visible"], "label": "seat0", "scope": "CLASS", "sitFrame": 7, "donePoint": "done0", "x": -60, "y": -15, "visible": false}]}], "plainObjects": [], "meta": {"app": "Phaser Editor 2D - Scene Editor", "url": "https://phasereditor2d.com", "contentType": "phasereditor2d.core.scene.SceneContentType", "version": 4}, "prefabProperties": [{"name": "moveToX", "label": "moveToX", "tooltip": "moveToX", "defValue": 0, "customDefinition": false, "type": {"id": "number"}}, {"name": "moveToY", "label": "moveToY", "tooltip": "moveToY", "defValue": 0, "customDefinition": false, "type": {"id": "number"}}]}