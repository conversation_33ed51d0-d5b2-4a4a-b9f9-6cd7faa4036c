{"id": "7554bcf3-656c-48ed-97c0-e431350f8ba5", "sceneType": "PREFAB", "settings": {"compilerInsertSpaces": true, "javaScriptInitFieldsInConstructor": true, "exportClass": true, "autoImport": true, "superClassName": "BaseContainer", "preloadMethodName": "", "preloadPackFiles": [], "createMethodName": "", "borderWidth": 1520, "borderHeight": 960}, "displayList": [{"type": "Container", "id": "92e98aa4-f6b1-4b7b-bee2-08bc6a7b7bef", "label": "container_1", "scaleX": -1, "list": [{"type": "Image", "id": "dd831ee5-3fa1-4d8e-adf4-273bf9ac6372", "label": "mat", "components": ["<PERSON><PERSON>", "ShowHint"], "Button.spriteName": "mat/mat_2", "Button.activeFrame": false, "Button.pixelPerfect": true, "ShowHint.text": "card_hint", "texture": {"key": "dojo", "frame": "mat/mat_2"}, "originX": 0.5018450184501845}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "c336d9d0-0e2b-4870-b1e8-2f5c9af610b1", "unlock": ["x", "y", "visible"], "label": "done1", "scope": "CLASS", "x": -49, "y": -70, "visible": false}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "28d94049-6730-4144-99b5-1c292edddc84", "unlock": ["x", "y", "visible"], "label": "done0", "scope": "CLASS", "x": 51, "y": -70, "visible": false}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "857d21c9-ecc6-4bdb-a431-92b238a712b0", "unlock": ["x", "y", "sit<PERSON>ram<PERSON>", "donePoint", "visible"], "label": "seat1", "scope": "CLASS", "sitFrame": 7, "donePoint": "done0", "x": -60, "y": -15, "visible": false}, {"prefabId": "3d402c83-0ef8-4993-bf56-3e076074ec9e", "id": "6221be4c-7fee-4007-96f7-1b1500e20dd7", "unlock": ["x", "y", "sit<PERSON>ram<PERSON>", "donePoint", "visible"], "label": "seat0", "scope": "CLASS", "sitFrame": 7, "donePoint": "done1", "x": 71, "y": -15, "visible": false}]}], "plainObjects": [], "meta": {"app": "Phaser Editor 2D - Scene Editor", "url": "https://phasereditor2d.com", "contentType": "phasereditor2d.core.scene.SceneContentType", "version": 4}, "prefabProperties": [{"name": "moveToX", "label": "moveToX", "tooltip": "moveToX", "defValue": 0, "customDefinition": false, "type": {"id": "number"}}, {"name": "moveToY", "label": "moveToY", "tooltip": "moveToY", "defValue": 0, "customDefinition": false, "type": {"id": "number"}}]}