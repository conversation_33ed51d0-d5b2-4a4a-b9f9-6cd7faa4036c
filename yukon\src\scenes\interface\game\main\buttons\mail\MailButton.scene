{"id": "4910867d-2158-40cd-881f-3b9764388f90", "sceneType": "PREFAB", "settings": {"compilerInsertSpaces": true, "javaScriptInitFieldsInConstructor": true, "exportClass": true, "autoImport": true, "superClassName": "BaseContainer", "preloadMethodName": "", "preloadPackFiles": [], "createMethodName": "", "borderWidth": 1520, "borderHeight": 960}, "displayList": [{"type": "Container", "id": "d11c660c-ce75-4718-9798-638b6318afa1", "label": "container_1", "list": [{"type": "Image", "id": "ed0ae6e3-4a15-4a13-98b7-d3d7d8d9d525", "label": "button", "components": ["<PERSON><PERSON>"], "Button.spriteName": "mail-button", "Button.callback": "() => scene.mail.show()", "Button.activeFrame": false, "texture": {"key": "main", "frame": "mail-button"}}, {"prefabId": "bcff41e9-3e1b-41d7-b3cc-26164bd4c892", "id": "e7e0fda0-1bb8-4953-8029-8bb4ad777a16", "unlock": ["x", "y", "visible"], "label": "notification", "scope": "CLASS", "x": 30, "y": -17, "visible": false}]}], "plainObjects": [], "meta": {"app": "Phaser Editor 2D - Scene Editor", "url": "https://phasereditor2d.com", "contentType": "phasereditor2d.core.scene.SceneContentType", "version": 5}}