-- Create tables first
CREATE TABLE `auth_tokens` (
  `userId` int(11) NOT NULL,
  `selector` char(36) NOT NULL,
  `validator` char(60) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Authentication tokens for saved logins';

CREATE TABLE `bans` (
  `id` int(11) NOT NULL,
  `userId` int(11) NOT NULL,
  `issued` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `expires` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `moderatorId` int(11) DEFAULT NULL,
  `message` varchar(60) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User ban records';

CREATE TABLE `buddies` (
  `userId` int(11) NOT NULL,
  `buddyId` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User buddies';

CREATE TABLE `cards` (
  `userId` int(11) NOT NULL,
  `cardId` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `memberQuantity` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User owned Card-Jitsu cards';

CREATE TABLE `furnitures` (
  `id` int(11) NOT NULL,
  `userId` int(11) NOT NULL,
  `furnitureId` int(11) NOT NULL,
  `x` smallint(6) NOT NULL DEFAULT 0,
  `y` smallint(6) NOT NULL DEFAULT 0,
  `rotation` smallint(6) NOT NULL DEFAULT 1,
  `frame` smallint(6) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Furniture placed inside igloos';

CREATE TABLE `furniture_inventories` (
  `userId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `quantity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User owned furniture';

CREATE TABLE `igloos` (
  `userId` int(11) NOT NULL,
  `type` int(11) NOT NULL DEFAULT 1,
  `flooring` int(11) NOT NULL DEFAULT 0,
  `music` int(11) NOT NULL DEFAULT 0,
  `location` int(11) NOT NULL DEFAULT 1,
  `locked` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User igloo settings';

CREATE TABLE `igloo_inventories` (
  `userId` int(11) NOT NULL,
  `iglooId` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User owned igloos';

CREATE TABLE `ignores` (
  `userId` int(11) NOT NULL,
  `ignoreId` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User ignores';

CREATE TABLE `inventories` (
  `userId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User owned clothing';

CREATE TABLE `pets` (
  `id` int(11) NOT NULL,
  `userId` int(11) NOT NULL,
  `typeId` int(11) NOT NULL,
  `name` varchar(12) NOT NULL,
  `adoptionDate` timestamp NOT NULL DEFAULT current_timestamp(),
  `energy` tinyint(3) NOT NULL DEFAULT 100,
  `health` tinyint(3) NOT NULL DEFAULT 100,
  `rest` tinyint(3) NOT NULL DEFAULT 100,
  `feedPostcardId` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='User pets';

CREATE TABLE `postcards` (
  `id` int(11) NOT NULL,
  `userId` int(11) NOT NULL,
  `senderId` int(11) DEFAULT NULL,
  `postcardId` int(11) NOT NULL,
  `sendDate` timestamp(3) NOT NULL DEFAULT current_timestamp(3),
  `details` varchar(255) CHARACTER SET latin1 DEFAULT NULL,
  `hasRead` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User postcards';

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(12) NOT NULL,
  `email` varchar(254) DEFAULT NULL,
  `password` char(60) NOT NULL,
  `loginKey` text DEFAULT NULL,
  `rank` tinyint(1) NOT NULL DEFAULT 1,
  `permaBan` tinyint(1) NOT NULL DEFAULT 0,
  `joinTime` timestamp NOT NULL DEFAULT current_timestamp(),
  `coins` int(11) NOT NULL DEFAULT 500,
  `head` int(11) NOT NULL DEFAULT 0,
  `face` int(11) NOT NULL DEFAULT 0,
  `neck` int(11) NOT NULL DEFAULT 0,
  `body` int(11) NOT NULL DEFAULT 0,
  `hand` int(11) NOT NULL DEFAULT 0,
  `feet` int(11) NOT NULL DEFAULT 0,
  `color` int(11) NOT NULL DEFAULT 1,
  `photo` int(11) NOT NULL DEFAULT 0,
  `flag` int(11) NOT NULL DEFAULT 0,
  `ninjaRank` tinyint(1) NOT NULL DEFAULT 0,
  `ninjaProgress` tinyint(3) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Users';

CREATE TABLE `worlds` (
  `id` varchar(100) NOT NULL,
  `population` smallint(3) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Server populations';

-- Insert initial data
INSERT INTO `worlds` (`id`, `population`) VALUES ('Blizzard', 0);
